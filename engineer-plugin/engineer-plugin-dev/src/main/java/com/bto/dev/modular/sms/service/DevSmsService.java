package com.bto.dev.modular.sms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bto.dev.modular.sms.param.DevSmsIdParam;
import com.bto.dev.modular.sms.param.DevSmsPageParam;
import com.bto.dev.modular.sms.param.DevSmsSendTencentParam;
import com.bto.dev.modular.sms.entity.DevSms;
import com.bto.dev.modular.sms.param.DevSmsSendAliyunParam;

import java.util.List;

/**
 * 短信Service接口
 * @date 2022/2/23 18:27
 **/
public interface DevSmsService extends IService<DevSms> {

    /**
     * 发送短信——阿里云
     * @date 2022/6/21 18:37
     **/
    void sendAliyun(DevSmsSendAliyunParam devSmsSendAliyunParam);

    /**
     * 发送短信——腾讯云
     * @date 2022/6/21 18:37
     **/
    void sendTencent(DevSmsSendTencentParam devSmsSendTencentParam);

    /**
     * 获取短信分页
     * @date 2022/4/24 20:08
     */
    Page<DevSms> page(DevSmsPageParam devSmsPageParam);

    /**
     * 删除短信
     * @date 2022/8/4 10:36
     **/
    void delete(List<DevSmsIdParam> devSmsIdParamList);

    /**
     * 获取短信详情
     * @date 2022/4/24 20:08
     */
    DevSms detail(DevSmsIdParam devSmsIdParam);

    /**
     * 获取短信详情
     * @date 2022/4/24 20:08
     */
    DevSms queryEntity(String id);
}
