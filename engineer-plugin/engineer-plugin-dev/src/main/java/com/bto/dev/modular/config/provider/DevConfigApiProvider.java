package com.bto.dev.modular.config.provider;

import org.springframework.stereotype.Service;
import com.bto.dev.api.DevConfigApi;
import com.bto.dev.modular.config.service.DevConfigService;

import javax.annotation.Resource;

/**
 * 配置API接口实现类
 * @date 2022/6/17 14:43
 **/
@Service
public class DevConfigApiProvider implements DevConfigApi {

    @Resource
    private DevConfigService devConfigService;

    @Override
    public String getValueByKey(String key) {
        return devConfigService.getValueByKey(key);
    }
}
