package com.bto.sys.modular.org.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bto.sys.modular.org.entity.SysOrg;
import com.bto.sys.modular.org.param.*;
import com.bto.sys.modular.user.entity.SysUser;

import java.util.List;

/**
 * 组织Service接口
 * @date 2022/4/21 18:35
 **/
public interface SysOrgService extends IService<SysOrg> {

    /**
     * 获取组织分页
     * @date 2022/4/24 20:08
     */
    Page<SysOrg> page(SysOrgPageParam sysOrgPageParam);

    /**
     * 获取组织树
     * @date 2022/4/24 20:08
     */
    List<Tree<String>> tree();

    /**
     * 添加组织
     * @date 2022/4/24 20:48
     */
    void add(SysOrgAddParam sysOrgAddParam);

    /**
     * 编辑组织
     * @date 2022/4/24 21:13
     */
    void edit(SysOrgEditParam sysOrgEditParam);

    /**
     * 删除组织
     * @date 2022/4/24 21:18
     */
    void delete(List<SysOrgIdParam> sysOrgIdParamList);

    /**
     * 获取组织详情
     * @date 2022/4/24 21:18
     */
    SysOrg detail(SysOrgIdParam sysOrgIdParam);

    /**
     * 获取组织详情
     * @date 2022/7/25 19:42
     **/
    SysOrg queryEntity(String id);

    /**
     * 获取所有组织
     * @date 2022/7/25 19:42
     **/
    List<SysOrg> getAllOrgList();

    /**
     * 根据组织全名称获取组织id，有则返回，无则创建
     * @date 2023/3/7 15:44
     **/
    String getOrgIdByOrgFullNameWithCreate(String orgFullName);

    /**
     * 根据id获取父子数据列表
     * @date 2022/8/15 14:55
     **/
    List<SysOrg> getParentAndChildListById(List<SysOrg> originDataList, String id, boolean includeSelf);

    /**
     * 根据id获取所有的子数据列表
     * @date 2022/8/15 14:55
     **/
    List<SysOrg> getChildListById(List<SysOrg> originDataList, String id, boolean includeSelf);

    /**
     * 根据id获取所有的父数据列表
     * @date 2022/8/15 14:55
     **/
    List<SysOrg> getParentListById(List<SysOrg> originDataList, String id, boolean includeSelf);

    /**
     * 根据id获取数据
     * @date 2022/8/15 14:55
     **/
    SysOrg getById(List<SysOrg> originDataList, String id);

    /**
     * 根据id获取父数据
     * @date 2022/8/15 14:55
     **/
    SysOrg getParentById(List<SysOrg> originDataList, String id);

    /**
     * 根据id获取子数据
     * @date 2022/8/15 14:55
     **/
    SysOrg getChildById(List<SysOrg> originDataList, String id);

    /**
     * 获取组织树选择器
     * @date 2022/4/24 20:08
     */
    List<Tree<String>> orgTreeSelector();

    /**
     * 获取组织列表选择器
     * @date 2022/7/22 13:34
     **/
    Page<SysOrg> orgListSelector(SysOrgSelectorOrgListParam sysOrgSelectorOrgListParam);

    /**
     * 获取用户选择器
     * @date 2022/4/24 20:08
     */
    Page<SysUser> userSelector(SysOrgSelectorUserParam sysOrgSelectorUserParam);
}
