package com.bto.sys.modular.user.param;

import lombok.Getter;
import lombok.Setter;

/**
 * 用户导入参数
 * @date 2022/7/8 13:22
 **/
@Getter
@Setter
public class SysUserImportParam {

    /** 账号 */
    private String account;

    /** 姓名 */
    private String name;

    /** 组织名称 */
    private String orgName;

    /** 职位名称 */
    private String positionName;

    /** 手机 */
    private String phone;

    /** 邮箱 */
    private String email;

    /** 主管名称 */
    private String directorName;

    /** 员工编号 */
    private String empNo;

    /** 入职日期 */
    private String entryDate;

    /** 职级 */
    private String positionLevel;

    /** 昵称 */
    private String nickname;

    /** 性别 */
    private String gender;

    /** 年龄 */
    private String age;

    /** 出生日期 */
    private String birthday;

    /** 民族 */
    private String nation;

    /** 籍贯 */
    private String nativePlace;

    /** 家庭住址 */
    private String homeAddress;

    /** 通信地址 */
    private String mailingAddress;

    /** 证件类型 */
    private String idCardType;

    /** 证件号码 */
    private String idCardNumber;

    /** 文化程度 */
    private String cultureLevel;

    /** 政治面貌 */
    private String politicalOutlook;

    /** 毕业院校 */
    private String college;

    /** 学历 */
    private String education;

    /** 学制 */
    private String eduLength;

    /** 学位 */
    private String degree;

    /** 家庭电话 */
    private String homeTel;

    /** 办公电话 */
    private String officeTel;

    /** 紧急联系人 */
    private String emergencyContact;

    /** 紧急联系人电话 */
    private String emergencyPhone;

    /** 紧急联系人地址 */
    private String emergencyAddress;
}
