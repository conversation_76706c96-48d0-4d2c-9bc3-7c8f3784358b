package com.bto.sys.modular.resource.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bto.sys.modular.resource.entity.SysModule;
import org.apache.ibatis.annotations.Param;

/**
 * 模块Mapper接口
 * @date 2022/4/21 18:37
 **/
public interface SysModuleMapper extends BaseMapper<SysModule> {

    /**
     * 删除数据并忽略插件（逻辑删除、租户拼接）
     * @date 2023/12/25 23:20
     */
    @InterceptorIgnore(tenantLine = "true")
    void deleteIgnoreInterceptor(@Param("ew") LambdaQueryWrapper<SysModule> lambdaQueryWrapper);

}
