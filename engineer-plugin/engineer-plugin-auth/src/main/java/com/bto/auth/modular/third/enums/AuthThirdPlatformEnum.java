package com.bto.auth.modular.third.enums;

import lombok.Getter;
import com.bto.common.exception.CommonException;

/**
 * 第三方登录平台枚举
 * @date 2021/10/11 14:02
 **/
@Getter
public enum AuthThirdPlatformEnum {

    /**
     * GITEE
     */
    GITEE("GITEE"),

    /**
     * WECHAT
     */
    WECHAT("WECHAT");

    private final String value;

    AuthThirdPlatformEnum(String value) {
        this.value = value;
    }

    public static void validate(String value) {
        boolean flag = GITEE.getValue().equals(value) || WECHAT.getValue().equals(value);
        if(!flag) {
            throw new CommonException("不支持的第三方平台：{}", value);
        }
    }
}
