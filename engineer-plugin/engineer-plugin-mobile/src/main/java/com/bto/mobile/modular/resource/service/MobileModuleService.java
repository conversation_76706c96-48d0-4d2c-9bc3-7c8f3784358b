package com.bto.mobile.modular.resource.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bto.mobile.modular.resource.entity.MobileModule;
import com.bto.mobile.modular.resource.param.module.MobileModuleAddParam;
import com.bto.mobile.modular.resource.param.module.MobileModuleEditParam;
import com.bto.mobile.modular.resource.param.module.MobileModuleIdParam;
import com.bto.mobile.modular.resource.param.module.MobileModulePageParam;

import java.util.List;

/**
 * 移动端模块Service接口
 * @date 2022/6/27 14:03
 **/
public interface MobileModuleService extends IService<MobileModule> {

    /**
     * 获取移动端模块分页
     * @date 2022/4/24 20:08
     */
    Page<MobileModule> page(MobileModulePageParam mobileModulePageParam);

    /**
     * 添加移动端模块
     * @date 2022/4/24 20:48
     */
    void add(MobileModuleAddParam mobileModuleAddParam);

    /**
     * 编辑移动端模块
     * @date 2022/4/24 21:13
     */
    void edit(MobileModuleEditParam mobileModuleEditParam);

    /**
     * 删除移动端模块
     * @date 2022/4/24 21:18
     */
    void delete(List<MobileModuleIdParam> mobileModuleIdParamList);

    /**
     * 获取移动端模块详情
     * @date 2022/4/24 21:18
     */
    MobileModule detail(MobileModuleIdParam mobileModuleIdParam);

    /**
     * 获取移动端模块详情
     * @date 2022/4/24 21:18
     */
    MobileModule queryEntity(String id);

    /**
     * 获取移动端模块选择器
     * @date 2023/7/15 21:52
     */
    List<JSONObject> mobileModuleSelector();
}
