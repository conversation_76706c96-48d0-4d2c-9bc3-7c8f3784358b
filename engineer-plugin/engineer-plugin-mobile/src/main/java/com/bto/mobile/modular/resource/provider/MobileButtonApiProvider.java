package com.bto.mobile.modular.resource.provider;

import com.bto.mobile.modular.resource.service.MobileButtonService;
import org.springframework.stereotype.Service;
import com.bto.mobile.api.MobileButtonApi;
import com.bto.mobile.modular.resource.entity.MobileButton;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 移动端按钮API接口提供者
 * @date 2023/1/31 10:12
 **/
@Service
public class MobileButtonApiProvider implements MobileButtonApi {

    @Resource
    private MobileButtonService mobileButtonService;

    @Override
    public List<String> listByIds(List<String> buttonIdList) {
        return mobileButtonService.listByIds(buttonIdList).stream().map(MobileButton::getCode).collect(Collectors.toList());
    }
}
