package com.bto.biz.modular.dict.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 业务字典编辑参数
 * @date 2022/7/30 21:48
 */
@Getter
@Setter
public class BizDictEditParam {

    /** id */
    @ApiModelProperty(value = "id", position = 1)
    @NotBlank(message = "id不能为空")
    private String id;

    /** 字典文字 */
    @ApiModelProperty(value = "字典文字", position = 2)
    @NotBlank(message = "dictLabel不能为空")
    private String dictLabel;

    /** 排序码 */
    @ApiModelProperty(value = "排序码", position = 3)
    @NotNull(message = "sortCode不能为空")
    private Integer sortCode;

    /** 扩展信息 */
    @ApiModelProperty(value = "扩展信息", position = 4)
    private String extJson;
}
