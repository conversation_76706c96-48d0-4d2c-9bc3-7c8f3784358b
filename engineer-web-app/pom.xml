<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.bto</groupId>
        <artifactId>engineer-app</artifactId>
        <version>2.0.0</version>
    </parent>

    <artifactId>engineer-web-app</artifactId>
    <packaging>jar</packaging>
    <description>主启动模块</description>

    <dependencies>

        <!-- test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- junit -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- dynamic-datasource -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        </dependency>

        <!-- mysql -->
         <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- 登录鉴权插件 -->
        <dependency>
            <groupId>com.bto</groupId>
            <artifactId>engineer-plugin-auth</artifactId>
        </dependency>

        <!-- 业务功能插件 -->
        <dependency>
            <groupId>com.bto</groupId>
            <artifactId>engineer-plugin-biz</artifactId>
        </dependency>

        <!-- C端功能插件 -->
        <dependency>
            <groupId>com.bto</groupId>
            <artifactId>engineer-plugin-client</artifactId>
        </dependency>

        <!-- 开发工具插件 -->
        <dependency>
            <groupId>com.bto</groupId>
            <artifactId>engineer-plugin-dev</artifactId>
        </dependency>

        <!-- 移动端管理插件 -->
        <dependency>
            <groupId>com.bto</groupId>
            <artifactId>engineer-plugin-mobile</artifactId>
        </dependency>

        <!-- 系统功能插件 -->
        <dependency>
            <groupId>com.bto</groupId>
            <artifactId>engineer-plugin-sys</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.5.12</version>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
